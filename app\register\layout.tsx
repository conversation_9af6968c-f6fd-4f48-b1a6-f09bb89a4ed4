import type { Metada<PERSON> } from "next";
import { redirect } from 'next/navigation'
import { auth } from '../auth';

// Site Metadata
export const metadata: Metadata = {
    title: `${process.env.NAME} - Register`,
    description: "Create a new account."
};

export default async function Layout_Register(_: Readonly<{
    children: React.ReactNode;
}>) {

    // - AUTH - //

    const session = await auth();

    // Only allow access if user is logged in AND is an admin
    if (!session || !session.user?.IsAdmin) {
        // redirect("/");
    }


    // - MAIN - //

    return _.children;

}
